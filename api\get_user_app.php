<?php
header('Content-Type: application/json');
require_once '../conn_waf.php';


$_SESSION['last_activity'] = time(); // 更新最后活动时间
ini_set('display_errors',1);
error_reporting(E_ALL);
// 数据库连接
try {

    $user_id = $_SESSION['user_id'];
    $personnel_type = $_SESSION['personnel_type'] ?? '';
    $query = "";
    $params = [];

    if ($_SESSION['is_admin']) {
        // 管理员获取所有应用
        $query = "SELECT id, application_name, url, public FROM application WHERE id>1";
    } elseif ($personnel_type == '民警') {
        // 民警获取 public 大于 0 以及有权限的应用
        $query = "SELECT DISTINCT a.id, a.application_name, a.url, a.public 
                  FROM application a 
                  LEFT JOIN permission p ON a.id = p.application_id AND p.user_id = ? 
                  WHERE a.public > 0 OR p.user_id = ? AND a.id >1" ;
        $params = [$user_id, $user_id];
    } else {
        // 其他用户获取 public 等于 0 以及有权限的应用
        $query = "SELECT DISTINCT a.id, a.application_name, a.url
                  FROM application a 
                  LEFT JOIN permission p ON a.id = p.application_id AND p.user_id = ? 
                  WHERE a.public = 0 OR p.user_id = ?  AND a.id>1";
        $params = [$user_id, $user_id];
    }

    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $types = str_repeat('i', count($params));
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $apps = $result->fetch_all(MYSQLI_ASSOC);

    echo json_encode(['status' => 'success', 'data' => $apps]);
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>