/* 添加呼吸动画关键帧 */
@keyframes breathe {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(234, 88, 12, 0.2);
    }
    50% {
      transform: scale(1.08);
      box-shadow: 0 0 0 8px rgba(234, 88, 12, 0.15);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(234, 88, 12, 0);
    }
  }
  
  /* 消息提示框样式 */
  .message-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
  }
  
  .message-modal.active {
    opacity: 1;
    pointer-events: auto;
  }

  .message-modal-content {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    transform: scale(0.9);
    transition: transform 0.3s ease;
  }
  
  .message-modal.active .message-modal-content {
    transform: scale(1);
  }
  
  .message-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background-color: #fff7ed; /* 淡橘色背景 */
    display: flex;
    align-items: center;
    border-radius: 16px 16px 0 0;
  }
  
  .message-modal-icon {
    width: 36px;
    height: 36px;
    background-color: #fed7aa; /* 淡橘色背景 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ea580c; /* 橘色图标 */
    margin-right: 15px;
    animation: breathe 1.5s infinite ease-in-out;
    position: relative;
  }
  
  .message-modal-icon::before {
    content: '\F431'; /* 信息图标 */
    font-family: 'bootstrap-icons';
    font-size: 1.3rem;
  }
  
  .message-modal-title { 
    font-size: 20px;
    font-weight: 600; 
    color: #ea580c; /* 橘色标题 */
  }
  
  .message-modal-body { 
    padding: 25px;
    font-size: 16px; 
    color: #4b5563; 
  }
  
  .message-modal-footer { 
    padding: 15px 20px; 
    border-top: 1px solid #eee; 
    display: flex; 
    justify-content: center; 
    border-radius: 0 0 16px 16px;
  }
  
  .message-modal-btn { 
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer; 
    font-weight: 500; 
    transition: background-color 0.2s ease; 
    border: none; 
    font-size: 16px;
  }
  
  .message-modal-ok { 
    background-color: #ea580c; /* 橘色按钮 */
    color: white; 
  }
  
  .message-modal-ok:hover { 
    background-color: #c2410c; /* 深橘色悬停效果 */
  }