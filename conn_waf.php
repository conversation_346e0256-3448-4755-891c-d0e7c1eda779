<?php
header('Content-Type: application/json; charset=utf-8');

header('Access-Control-Allow-Origin: http://localhost:5173'); 
// 允许携带凭证
header('Access-Control-Allow-Credentials: true'); 
// 允许的请求方法
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS'); 
// 允许的请求头
header('Access-Control-Allow-Headers: Content-Type, Authorization'); 

$servername = "localhost";
$username = "root";
$password = "YC@yc110";
$dbname = "application";

// 使用mysqli扩展连接MySQL
$conn = mysqli_connect($servername, $username, $password, $dbname);
// 检查连接
// 在数据库连接后添加
if ($conn->connect_error) {
    die("数据库连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");



function waf_check_all_inputs() {
    $request_method = $_SERVER['REQUEST_METHOD'];
    
    // 根据请求方法获取输入数据
    $inputs = [];
    if ($request_method === 'GET') {
        $inputs = $_GET;
    } elseif ($request_method === 'POST') {
        $inputs = $_POST;
    }
    
    // 检查所有输入参数
    foreach ($inputs as $key => $value) {
        if (!waf_check($value)) {
            die(json_encode(['status' => 'error', 'message' => '参数['.$key.']包含非法字符']));
        }
    }
    return true;
}

function waf_check($input) {
    $keywords = array(
        "select", "insert", "update", "delete", "drop", 
        "truncate", "union", "sleep", "benchmark", "--", 
        "/*", "*/", "<", ">", "script", "onerror", "onload"
    );
    
    if (is_array($input)) {
        foreach ($input as $item) {
            if (!waf_check($item)) {
                return false;
            }
        }
        return true;
    }
    
    // 将输入和关键字都转换为小写进行比较
    $lowerInput = strtolower($input);
    foreach($keywords as $keyword) {
        if(strpos($lowerInput, strtolower($keyword)) !== false) {
            return false;
        }
    }
    return true;
}

function logOperation($conn, $user_id, $application_id, $operation) {
    $ip = $_SERVER['REMOTE_ADDR'] ;
    $log_stmt = $conn->prepare("INSERT INTO log (user_id, application_id, operation, ip) VALUES (?, ?, ?, ?)");
    $log_stmt->bind_param("iiss", $user_id, $application_id, $operation, $ip);
    $log_stmt->execute();
}


// 自动执行检查
waf_check_all_inputs();
session_start();
?>