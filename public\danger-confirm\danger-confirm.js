/**
 * 危险操作确认对话框类
 * 用于创建和管理危险操作的确认提示框
 */
class DangerConfirm {
    constructor() {
        // 初始化方法：创建模态框DOM结构并绑定事件
        this.createModal();
        
        // 获取模态框相关元素的引用
        this.modal = document.getElementById('danger-modal');            // 整个模态框容器
        this.titleEl = document.getElementById('danger-modal-title');    // 标题元素
        this.messageEl = document.getElementById('danger-modal-message');// 消息内容元素
        this.confirmBtn = document.getElementById('danger-modal-confirm');// 确认按钮
        this.cancelBtn = document.getElementById('danger-modal-cancel');  // 取消按钮
        
        // 绑定事件处理函数，确保`this`指向当前实例
        this.confirmBtn.addEventListener('click', this.handleConfirm.bind(this));
        this.cancelBtn.addEventListener('click', this.handleCancel.bind(this));
        this.modal.addEventListener('click', this.handleOutsideClick.bind(this));
        
        // 存储Promise的resolve回调函数，用于返回用户操作结果
        this.resolveCallback = null;
    }
    
    // 创建模态框的HTML结构并添加到页面中
    createModal() {
        const modalHTML = `
            <div class="danger-modal" id="danger-modal">
                <div class="danger-modal-content">
                    <div class="danger-modal-header">
                        <div class="danger-modal-icon"></div>
                        <h3 class="danger-modal-title" id="danger-modal-title"></h3>
                    </div>
                    <div class="danger-modal-body" id="danger-modal-message">
                    </div>
                    <div class="danger-modal-footer">
                        <button class="danger-modal-btn danger-modal-cancel" id="danger-modal-cancel">
                        </button>
                        <button class="danger-modal-btn danger-modal-confirm" id="danger-modal-confirm">
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // 将HTML字符串插入到页面的body元素末尾
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    /**
     * 显示确认对话框
     * @param {Object} options - 配置选项
     * @param {string} options.title - 对话框标题
     * @param {string} options.message - 对话框消息内容
     * @param {string} options.confirmText - 确认按钮文本
     * @param {string} options.cancelText - 取消按钮文本
     * @returns {Promise<boolean>} - 返回Promise，用户确认时resolve(true)，取消时resolve(false)
     */
    show(options = {}) {
        // 设置默认选项，用户提供的选项会覆盖默认值
        const defaults = {
            title: '危险操作确认',
            message: '此操作将永久删除数据，不可恢复！',
            confirmText: '确认',
            cancelText: '取消'
        };
        
        const opts = { ...defaults, ...options };
        
        // 更新对话框的内容为用户指定的内容
        this.titleEl.textContent = opts.title;
        this.messageEl.textContent = opts.message;
        this.confirmBtn.textContent = opts.confirmText;
        this.cancelBtn.textContent = opts.cancelText;
        
        // 使用setTimeout延迟添加active类，确保动画效果正常触发
        setTimeout(() => {
            this.modal.classList.add('active'); // 添加active类使模态框显示并触发动画
        }, 10);
        
        // 返回Promise，用于异步获取用户操作结果
        return new Promise(resolve => {
            this.resolveCallback = resolve; // 存储resolve回调供后续使用
        });
    }
    
    // 处理确认按钮点击事件
    handleConfirm() {
        this.hide(); // 隐藏模态框
        this.resolveCallback(true); // 通知Promise用户已确认
    }
    
    // 处理取消按钮点击事件
    handleCancel() {
        this.hide(); // 隐藏模态框
        this.resolveCallback(false); // 通知Promise用户已取消
    }
    
    // 处理模态框外部点击事件
    handleOutsideClick(event) {
        // 只有当点击的是模态框背景（而非内容区域）时才关闭
        if (event.target === this.modal) {
            this.hide(); // 隐藏模态框
            this.resolveCallback(false); // 通知Promise用户已取消
        }
    }
    
    // 隐藏对话框
    hide() {
        this.modal.classList.remove('active'); // 移除active类，触发隐藏动画
    }
}

// 将DangerConfirm类挂载到window对象，使其可以在全局范围内使用
window.DangerConfirm = DangerConfirm;