/* 引入本地 Bootstrap Icons 字体文件 */
@font-face {
    font-family: 'bootstrap-icons';
    src: url('../fonts/bootstrap-icons.woff2') format('woff2'); /* 指定字体文件路径和格式 */
    font-weight: normal;
    font-style: normal;
}

/* 定义所有图标类的基础样式 */
.bi::before {
    display: inline-block;          /* 以行内块级元素显示，避免影响文本布局 */
    font-family: 'bootstrap-icons'; /* 指定使用的字体 */
    font-style: normal;             /* 正常字体样式 */
    font-weight: normal;            /* 正常字体粗细 */
    font-variant: normal;           /* 正常字体变体 */
    text-transform: none;           /* 不转换文本大小写 */
    line-height: 1;                 /* 行高设为1，确保图标垂直居中 */
    vertical-align: -0.125em;       /* 微调垂直对齐，使其与文本更好地对齐 */
    -webkit-font-smoothing: antialiased; /* 优化字体在webkit浏览器中的渲染 */
    -moz-osx-font-smoothing: grayscale;  /* 优化字体在Firefox和Mac OS中的渲染 */
}

/* 定义警告图标样式 */
.bi-exclamation-octagon-fill::before {
    content: '\F335'; /* 警告图标对应的Unicode编码 */
}

/* 危险提示框整体容器样式 */
.danger-modal {
    position: fixed;           /* 固定定位，覆盖整个视口 */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明背景遮罩 */
    display: flex;             /* 使用Flex布局实现垂直和水平居中 */
    align-items: center;
    justify-content: center;
    z-index: 9999;             /* 确保在最上层显示 */
    opacity: 0;                /* 默认不可见 */
    pointer-events: none;      /* 默认不响应鼠标事件 */
    transition: opacity 0.3s ease; /* 添加淡入淡出动画 */
}

/* 当模态框激活时的样式 */
.danger-modal.active {
    opacity: 1;                /* 显示模态框 */
    pointer-events: auto;      /* 响应鼠标事件 */
}

/* 模态框内容区域样式 */
.danger-modal-content {
    background-color: white;   /* 白色背景 */
    border-radius: 8px;        /* 圆角边框 */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); /* 阴影效果 */
    max-width: 400px;          /* 最大宽度 */
    width: 90%;                /* 宽度为父容器的90% */
    transform: scale(0.9);     /* 默认缩小显示 */
    transition: transform 0.3s ease; /* 添加缩放动画 */
}

/* 当模态框激活时，内容区域的样式 */
.danger-modal.active .danger-modal-content {
    transform: scale(1);       /* 恢复正常大小，产生弹出效果 */
}

/* 模态框头部样式 */
.danger-modal-header {
    padding: 5px 10px;        /* 内边距 */
    border-bottom: 1px solid #eee; /* 底部边框 */
    background-color: #fef2f2; /* 浅红色背景 */
    display: flex;             /* 使用Flex布局 */
    align-items: center;       /* 垂直居中对齐 */
}

/* 警告图标容器样式 */
.danger-modal-icon {
    width: 32px;               /* 宽度 */
    height: 32px;              /* 高度 */
    background-color: #fecaca; /* 浅红色背景 */
    border-radius: 50%;        /* 圆形 */
    display: flex;             /* 使用Flex布局 */
    align-items: center;       /* 垂直居中 */
    justify-content: center;   /* 水平居中 */
    color: #dc2626;            /* 图标颜色 */
    margin-right: 12px;        /* 右侧间距 */
}

/* 警告图标内容 */
.danger-modal-icon::before {
    content: '\F335';          /* 警告图标对应的Unicode编码 */
    font-family: 'bootstrap-icons'; /* 指定使用的字体 */
    font-size: 1.25rem;        /* 图标大小 */
}

/* 模态框标题样式 */
.danger-modal-title {
    font-size: 18px;           /* 字体大小 */
    font-weight: 600;          /* 字体粗细 */
    color: #dc2626;            /* 红色文本 */
}

/* 模态框主体内容样式 */
.danger-modal-body {
    padding: 20px;             /* 内边距 */
    font-size: 16px;           /* 字体大小 */
    color: #4b5563;            /* 深灰色文本 */
}

/* 模态框底部按钮区域样式 */
.danger-modal-footer {
    padding: 15px 20px;        /* 内边距 */
    border-top: 1px solid #eee; /* 顶部边框 */
    display: flex;             /* 使用Flex布局 */
    justify-content: flex-end; /* 按钮靠右排列 */
    gap: 10px;                 /* 按钮之间的间距 */
}

/* 模态框按钮基础样式 */
.danger-modal-btn {
    padding: 8px 16px;         /* 内边距 */
    border-radius: 4px;        /* 圆角 */
    cursor: pointer;           /* 鼠标指针样式 */
    font-weight: 500;          /* 字体粗细 */
    transition: background-color 0.2s ease; /* 背景色过渡动画 */
    border: none;              /* 无边框 */
}

/* 取消按钮样式 */
.danger-modal-cancel {
    border: 1px solid #d1d5db; /* 浅灰色边框 */
    color: #4b5563;            /* 深灰色文本 */
}

/* 取消按钮悬停效果 */
.danger-modal-cancel:hover {
    background-color: #f3f4f6; /* 悬停时的背景色 */
}

/* 确认按钮样式 */
.danger-modal-confirm {
    background-color: #dc2626; /* 红色背景 */
    color: white;              /* 白色文本 */
}

/* 确认按钮悬停效果 */
.danger-modal-confirm:hover {
    background-color: #b91c1c; /* 悬停时的更深红色 */
}