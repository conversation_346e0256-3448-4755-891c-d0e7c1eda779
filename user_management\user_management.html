<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            display: flex;
            height: 100vh;
            overflow: hidden;
            background-color: #f5f7fa;
        }

        .dialog {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 800px;
            width: 90%;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button[type="submit"] {
            background-color: #4CAF50;
            color: white;
        }

        button[type="button"] {
            background-color: #f44336;
            color: white;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background-color: #333744;
            color: #fff;
            overflow-y: auto;
            border-right: 1px solid #e5e7eb;
        }
        
        .sidebar-header {
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px solid #4b5563;
            display: flex;
            align-items: center;
        }
        
        .sidebar-header i {
            margin-right: 8px;
        }
        
        /* 单位树样式 */
        .unit-tree {
            padding: 10px 0;
        }
        
        .unit-item {
            padding: 10px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
        }
        
        .unit-item:hover {
            background-color: #4b5563;
        }
        
        .unit-item.active {
            background-color: #165DFF;
        }
        
        .unit-icon {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }
        
        .unit-children {
            margin-left: 24px;
            border-left: 1px dashed #4b5563;
        }
        
        .caret {
            display: inline-block;
            width: 0;
            height: 0;
            margin-left: 5px;
            vertical-align: middle;
            border-top: 4px dashed;
            border-top: 4px solid\9;
            border-right: 4px solid transparent;
            border-left: 4px solid transparent;
            transition: transform 0.2s;
        }
        
        .caret-open {
            transform: rotate(-180deg);
        }
        
        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .content-header {
            padding: 20px;
            background-color: #fff;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        
        .content-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: #165DFF;
            color: #fff;
        }
        
        .btn-primary:hover {
            background-color: #0e48cc;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid #e5e7eb;
            color: #333;
        }
        
        .btn-outline:hover {
            background-color: #f5f7fa;
        }
        
        /* 用户列表样式 */
        .user-table-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .user-table th,
        .user-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .user-table th {
            background-color: #f5f7fa;
            font-weight: 600;
            color: #333;
        }
        
        .user-table tr:last-child td {
            border-bottom: none;
        }
        
        .user-table tr:hover {
            background-color: #f9fafb;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 5px;
        }
        
        .page-item {
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .page-item.active {
            background-color: #165DFF;
            color: #fff;
            border-color: #165DFF;
        }
        
        .page-item:hover:not(.active) {
            background-color: #f5f7fa;
        }
        
        /* 空状态样式 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: #86909C;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .empty-text {
            font-size: 16px;
        }
        
        /* 加载状态样式 */
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #165DFF;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .required-label::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
    

    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fa fa-sitemap"></i>用户管理
        </div>
        <div class="unit-tree" id="unit-tree"></div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-header">
            <div class="content-title" id="current-unit-title">请选择单位</div>
            <div class="content-actions">
                <button class="btn btn-primary" onclick="openAddUserDialog()">
                    <i class="fa fa-plus"></i> 添加用户
                </button>

            </div>
        </div>
        <div class="user-table-container" id="user-container">
            <div class="empty-state">
                <div class="empty-icon">👥</div>
                <div class="empty-text">请从左侧选择一个单位查看用户</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟单位数据
        const mockUnitData = {
            "id": 1,
            "unit_name": "河口县",
            "code": "511621",
            "parent_id": 0,
            "sort_order": 1,
            "children": [
                {
                    "id": 2,
                    "unit_name": "情指中心",
                    "code": "511622",
                    "parent_id": 1,
                    "sort_order": 1,
                    "children": [
                        {
                            "id": 4,
                            "unit_name": "科技通信中队",
                            "code": "511625",
                            "parent_id": 2,
                            "sort_order": 4
                        }
                    ]
                },
                {
                    "id": 3,
                    "unit_name": "治安大队",
                    "code": "511623",
                    "parent_id": 1,
                    "sort_order": 3
                },
                {
                    "id": 5,
                    "unit_name": "刑侦大队",
                    "code": "511624",
                    "parent_id": 1,
                    "sort_order": 2,
                    "children": [
                        {
                            "id": 6,
                            "unit_name": "一中队",
                            "code": "511626",
                            "parent_id": 5,
                            "sort_order": 1
                        },
                        {
                            "id": 7,
                            "unit_name": "二中队",
                            "code": "511627",
                            "parent_id": 5,
                            "sort_order": 2
                        }
                    ]
                }
            ]
        };

        // 模拟用户数据
        const mockUserData = {
            1: [
                { id: 101, name: "张三", idCard: "511621199001011234", phone: "13800138000", role: "管理员", status: "在职" },
                { id: 102, name: "李四", idCard: "511621199102021235", phone: "13900139000", role: "普通用户", status: "在职" },
                { id: 103, name: "王五", idCard: "511621199203031236", phone: "13700137000", role: "操作员", status: "休假" }
            ],
            2: [
                { id: 201, name: "赵六", idCard: "511622199304041237", phone: "13600136000", role: "管理员", status: "在职" },
                { id: 202, name: "钱七", idCard: "511622199405051238", phone: "13500135000", role: "普通用户", status: "在职" }
            ],
            5: [
                { id: 501, name: "孙八", idCard: "511624199506061239", phone: "13400134000", role: "操作员", status: "在职" },
                { id: 502, name: "周九", idCard: "511624199607071240", phone: "13300133000", role: "普通用户", status: "离职" },
                { id: 503, name: "吴十", idCard: "511624199708081241", phone: "13200132000", role: "管理员", status: "在职" }
            ],
            6: [
                { id: 601, name: "郑十一", idCard: "511626199809091242", phone: "13100131000", role: "普通用户", status: "在职" }
            ]
        };

        // 移除模拟单位数据
        // const mockUnitData = { ... };

        // 从接口获取单位数据
        async function fetchUnitData() {
            try {
                const response = await fetch('../api/get_unit_info.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                if (!response.ok) {
                    throw new Error('网络响应异常');
                }
                const data = await response.json();
                if (data.status === 'success') {
                    return data.data;
                }
                throw new Error('获取单位数据失败');
            } catch (error) {
                console.error('获取单位数据时出错:', error);
                return null;
            }
        }

        // 从接口获取用户数据
        async function fetchUserData(unitId) {
            try {
                const response = await fetch('../api/user_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: 'list', unitId: unitId })
                });
                if (!response.ok) {
                    throw new Error('网络响应异常');
                }
                const data = await response.json();
                if (data.status === 'success') {
                    return data.users;
                }
                throw new Error('获取用户数据失败');
            } catch (error) {
                console.error('获取用户数据时出错:', error);
                return [];
            }
        }

        // 渲染单位树
        function renderUnitTree(unit, parentElement = document.getElementById('unit-tree'), level = 1) {
            const unitElement = document.createElement('div');
            unitElement.className = 'unit-item';
            unitElement.dataset.id = unit.id;
            unitElement.innerHTML = `
                <div class="unit-icon">
                    ${unit.children && unit.children.length > 0 ? '<i class="fa fa-folder"></i>' : '<i class="fa fa-folder-o"></i>'}
                </div>
                <span>${unit.unit_name}</span>
                ${unit.children && unit.children.length > 0 ? '<span class="caret"></span>' : ''}
            `;
            
            parentElement.appendChild(unitElement);
            
            // 添加点击事件
            unitElement.addEventListener('click', () => {
                // 移除其他单位的选中状态
                document.querySelectorAll('.unit-item.active').forEach(item => {
                    item.classList.remove('active');
                });
                
                // 添加当前单位的选中状态
                unitElement.classList.add('active');
                
                // 加载并显示该单位的用户
                loadUsers(unit.id, unit.unit_name);
            });
            
            // 如果有子单位，递归渲染
            if (unit.children && unit.children.length > 0) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'unit-children';
                parentElement.appendChild(childrenContainer);
                
                // 按sort_order排序子单位
                const sortedChildren = [...unit.children].sort((a, b) => a.sort_order - b.sort_order);
                
                sortedChildren.forEach(child => {
                    renderUnitTree(child, childrenContainer, level + 1);
                });
                
                // 添加展开/折叠功能
                const caret = unitElement.querySelector('.caret');
                caret.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡到单位项
                    
                    if (childrenContainer.style.display === 'none') {
                        childrenContainer.style.display = 'block';
                        caret.classList.add('caret-open');
                        unitElement.querySelector('.unit-icon i').className = 'fa fa-folder-open';
                    } else {
                        childrenContainer.style.display = 'none';
                        caret.classList.remove('caret-open');
                        unitElement.querySelector('.unit-icon i').className = 'fa fa-folder';
                    }
                });
                
                // 默认展开第一层
                if (level === 1) {
                    childrenContainer.style.display = 'block';
                    caret.classList.add('caret-open');
                    unitElement.querySelector('.unit-icon i').className = 'fa fa-folder-open';
                } else {
                    childrenContainer.style.display = 'none';
                }
            }
        }

        // 加载并显示用户
        async function loadUsers(unitId, unitName) {
            const userContainer = document.getElementById('user-container');
            const currentUnitTitle = document.getElementById('current-unit-title');

            // 更新当前单位标题
            currentUnitTitle.textContent = `${unitName} - 用户列表`;

            // 显示加载状态
            userContainer.innerHTML = `
                <div class="loading-state">
                    <div class="loader"></div>
                    <div>加载中...</div>
                </div>
            `;

            const users = await fetchUserData(unitId);

            if (users.length === 0) {
                // 没有用户数据
                userContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">👤</div>
                        <div class="empty-text">该单位下暂无用户</div>
                    </div>
                `;
            } else {
                // 有用户数据，渲染表格
                renderUserTable(users, userContainer);
            }
        }

        // 渲染用户表格
        function renderUserTable(users, container) {
            container.innerHTML = `
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>用户姓名</th>
                            <th>身份证号</th>
                            <th>手机号码</th>
                            <th>用户角色</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.name}</td>
                                <td>${user.idCard}</td>
                                <td>${user.phone}</td>
                                <td>${user.role}</td>
                                <td>${getStatusBadge(user.status)}</td>
                                <td>
                                    <button class="btn btn-outline" style="padding: 4px 8px; margin-right: 5px;">
                                        <i class="fa fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-outline" style="padding: 4px 8px;">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                <div class="pagination">
                    <div class="page-item">上一页</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">下一页</div>
                </div>
            `;
        }

        // 获取状态徽章
        function getStatusBadge(status) {
            switch (status) {
                case '在职':
                    return '<span style="padding: 2px 6px; background-color: #e6ffed; color: #00b42a; border-radius: 3px;">在职</span>';
                case '休假':
                    return '<span style="padding: 2px 6px; background-color: #fff7e6; color: #ff7d00; border-radius: 3px;">休假</span>';
                case '离职':
                    return '<span style="padding: 2px 6px; background-color: #fff2f0; color: #f53f3f; border-radius: 3px;">离职</span>';
                default:
                    return status;
            }
        }

        // 页面加载完成后从接口获取数据并渲染单位树
        document.addEventListener('DOMContentLoaded', async function() {
            const unitData = await fetchUnitData();
            if (unitData) {
                renderUnitTree(unitData);
            }
        });
    </script>
</body>
</html>

<div id="addUserDialog" class="dialog">
    <form id="addUserForm">
        <div class="form-grid">
            <div class="form-group">
                <label for="name" class="required-label">姓名</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="idNumber">身份证号</label>
                <input type="text" id="idNumber" name="idNumber">
            </div>
            <div class="form-group">
                <label for="phone1" class="required-label">电话号码</label>
                <input type="text" id="phone1" name="phone1" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password">
            </div>
            <div class="form-group">
                <label for="birthdate">出生日期</label>
                <input type="date" id="birthdate" name="birthdate">
            </div>
            <div class="form-group">
                <label for="gender" class="required-label">性别</label>
                <select id="gender" name="gender" required>
                    <option value="male">男</option>
                    <option value="female">女</option>
                </select>
            </div>
            <div class="form-group">
                <label for="shortNumber">短号</label>
                <input type="text" id="shortNumber" name="shortNumber">
            </div>
            <div class="form-group">
                <label for="phone2">电话号码2</label>
                <input type="text" id="phone2" name="phone2">
            </div>
            <div class="form-group">
                <label for="phone3">电话号码3</label>
                <input type="text" id="phone3" name="phone3">
            </div>
            <div class="form-group">
                <label for="landline">座机</label>
                <input type="text" id="landline" name="landline">
            </div>
            <div class="form-group">
                <label for="compiledUnit" class="required-label">编制单位</label>
                <input type="text" id="compiledUnit" name="compiledUnit">
            </div>
            <div class="form-group">
                <label for="workUnit">工作单位</label>
                <input type="text" id="workUnit" name="workUnit">
            </div>
            <div class="form-group">
                <label for="startWorkDate">参加工作时间</label>
                <input type="date" id="startWorkDate" name="startWorkDate">
            </div>
            <div class="form-group">
                <label for="politicalStatus">政治面貌</label>
                <input type="text" id="politicalStatus" name="politicalStatus">
            </div>
            <div class="form-group">
                <label for="joinOrganizationDate">加入组织时间</label>
                <input type="date" id="joinOrganizationDate" name="joinOrganizationDate">
            </div>
            <div class="form-group">
                <label for="personnelIdentity" class="required-label">人员身份</label>
    
                <select id="personnelIdentity" name="personnelIdentity">
                    <option>民警</option>
                    <option>职工</option>
                    <option>辅警</option>
                    <option>机关工勤</option>
                    <option>其他</option>
                    <!-- 可以根据实际需求添加更多选项 -->
                </select>
            </div>
            <div class="form-group">
                <label for="policeNumber">警号（辅警号）</label>
                <input type="text" id="policeNumber" name="policeNumber">
            </div>
            <div class="form-group">
                <label for="isAuxiliary">是否带辅</label>
                <select id="isAuxiliary" name="isAuxiliary">
                    <option value="">请选择</option>
                    <option value="yes">是</option>
                    <option value="no">否</option>
                </select>
            </div>
            <div class="form-group">
                <label for="personnelStatus" class="required-label">人员状态</label>
                <select id="personnelStatus" name="personnelStatus">
                    <option>在职</option>
                    <option>调离</option>
                    <option>退休</option>
                    <option>开除</option>
                    <option>借调出局</option>
                    <!-- 可以根据实际需求添加更多选项 -->
                </select>
            </div>
            <div class="form-group">
                <label for="rank">职级</label>
                <input type="text" id="rank" name="rank">
            </div>
            <div class="form-group">
                <label for="currentRankDate">任现职级时间</label>
                <input type="date" id="currentRankDate" name="currentRankDate">
            </div>
            <div class="form-group">
                <label for="position">职务</label>
                <input type="text" id="position" name="position">
            </div>
            <div class="form-group">
                <label for="currentPositionDate">任现职务时间</label>
                <input type="date" id="currentPositionDate" name="currentPositionDate">
            </div>
            <div class="form-group">
                <label for="sortOrder">本单位人员排序值</label>
                <input type="number" id="sortOrder" name="sortOrder">
            </div>
        </div>
        <button type="button" onclick="closeAddUserDialog()">取消</button>
        <button type="submit">提交</button>
    </form>
</div>


<script>
    function openAddUserDialog() {
        document.getElementById('addUserDialog').style.display = "block";
    }

    function closeAddUserDialog() {
        document.getElementById('addUserDialog').style.display = "none";
    }

    document.getElementById('addUserForm').addEventListener('submit', function(event) {
        event.preventDefault();
        // 这里可以添加提交表单的逻辑
        closeAddUserDialog();
    });
</script>
    