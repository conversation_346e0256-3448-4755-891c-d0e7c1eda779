/**
 * 信息提示框类
 * 用于创建和管理信息提示框，替代原生的alert
 */
class MessageAlert {
  constructor() {
    // 初始化方法：创建模态框DOM结构并绑定事件
    this.createModal();
    
    // 获取模态框相关元素的引用
    this.modal = document.getElementById('message-modal');
    this.titleEl = document.getElementById('message-modal-title');
    this.messageEl = document.getElementById('message-modal-message');
    this.okBtn = document.getElementById('message-modal-ok');
    
    // 绑定事件处理函数，确保`this`指向当前实例
    this.okBtn.addEventListener('click', this.handleOk.bind(this));
    this.modal.addEventListener('click', this.handleOutsideClick.bind(this));
    
    // 存储Promise的resolve回调函数，用于返回用户操作结果
    this.resolveCallback = null;
  }
  
  // 创建模态框的HTML结构并添加到页面中
  createModal() {
    const modalHTML = `
      <div class="message-modal" id="message-modal">
        <div class="message-modal-content">
          <div class="message-modal-header">
            <div class="message-modal-icon"></div>
            <h3 class="message-modal-title" id="message-modal-title"></h3>
          </div>
          <div class="message-modal-body" id="message-modal-message"></div>
          <div class="message-modal-footer">
            <button class="message-modal-btn message-modal-ok" id="message-modal-ok">
            </button>
          </div>
        </div>
      </div>
    `;
    
    // 将HTML字符串插入到页面的body元素末尾
    document.body.insertAdjacentHTML('beforeend', modalHTML);
  }
  
  /**
   * 显示信息提示框
   * @param {Object|string} options - 配置选项或直接作为消息内容
   * @param {string} [options.title] - 对话框标题
   * @param {string} [options.message] - 对话框消息内容
   * @param {string} [options.okText] - 确定按钮文本
   * @returns {Promise<void>} - 返回Promise，用户点击确定后resolve
   */
  show(options = {}) {
    // 支持直接传入消息字符串
    const opts = typeof options === 'string' 
      ? { message: options } 
      : { ...options };
    
    // 设置默认选项
    const defaults = {
      title: '提示',
      message: '操作成功！',
      okText: '确定'
    };
    
    const mergedOpts = { ...defaults, ...opts };
    
    // 更新对话框的内容
    this.titleEl.textContent = mergedOpts.title;
    this.messageEl.textContent = mergedOpts.message;
    this.okBtn.textContent = mergedOpts.okText;
    
    // 显示模态框并触发动画
    setTimeout(() => {
      this.modal.classList.add('active');
    }, 10);
    
    // 返回Promise，用于异步获取用户操作结果
    return new Promise(resolve => {
      this.resolveCallback = resolve;
    });
  }
  
  // 处理确定按钮点击事件
  handleOk() {
    this.hide();
    this.resolveCallback();
  }
  
  // 处理模态框外部点击事件
  handleOutsideClick(event) {
    // 默认不允许点击外部关闭，如需允许，取消下面的注释
    /*
    if (event.target === this.modal) {
      this.hide();
      this.resolveCallback();
    }
    */
  }
  
  // 隐藏对话框
  hide() {
    this.modal.classList.remove('active');
  }
}

// 将MessageAlert类挂载到window对象，使其可以在全局范围内使用
window.MessageAlert = MessageAlert;