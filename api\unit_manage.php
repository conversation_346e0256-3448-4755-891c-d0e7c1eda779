<?php

require_once '../config.php';
//定义应用
$app_id=2;
// 检查管理员权限
header('Content-Type: application/json');


if(!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    die(json_encode(['status' => 'error', 'message' => '无权限访问']));
}
//检查上传参数是否有注入风险
waf_check($_POST);
$action = $_POST['action'] ?? '';

switch($action) {

        
    case 'add':
        // 添加单位
        
        $unitName = $_POST['unit_name'] ?? '';
        $parentId = $_POST['parent_id'] ?? null;
        $afterUnitId = $_POST['after_unit_id']??0;
        $code = $_POST['code'] ?? null;
        
        if(empty($unitName)) {
            echo json_encode(['status' => 'error', 'message' => '单位名称不能为空']);
            exit;
        }
        
        if(empty($parentId)) {
            echo json_encode(['status' => 'error', 'message' => '必须选择上级单位']);
            exit;
        }
        
    
        // 检查单位名是否唯一
        $checkUniqueSql = "SELECT COUNT(*) as count FROM unit WHERE unit_name = ?";
        $checkUniqueStmt = $conn->prepare($checkUniqueSql);
        $checkUniqueStmt->bind_param('s', $unitName);
        $checkUniqueStmt->execute();
        $uniqueResult = $checkUniqueStmt->get_result();
        $uniqueCount = $uniqueResult->fetch_assoc()['count'];
    
        if($uniqueCount > 0) {
            echo json_encode(['status' => 'error', 'message' => '该单位名已存在，请使用其他名称']);
            exit;
        }
    
        // 更新后续单位的排序值
        $updateSql = "UPDATE unit SET sort_order = sort_order + 1 WHERE parent_id = ? AND sort_order > ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param('ii', $parentId, $afterUnitId);
        $updateStmt->execute();

        // 插入新单位
        $sql = "INSERT INTO unit (unit_name, parent_id, sort_order,code) VALUES (?, ?, ?,?)";
        $stmt = $conn->prepare($sql);
        $SortOrder = $afterUnitId + 1;
        $stmt->bind_param('siis', $unitName, $parentId, $SortOrder,$code);

        // 添加单位成功后的日志记录
        if ($stmt->execute()) {
            $operation = sprintf(
                "添加单位: 新名称=%s, 上级单位ID=%d",
                $unitName,
                $parentId
            );
            logOperation($conn, $_SESSION['user_id'],2, $operation);
            echo json_encode(['status' => 'success', 'message' => '单位添加成功']);
        }

        // 编辑单位成功后的日志记录


        break;
        
    case 'edit':
        // 编辑单位
        $unitId = $_POST['id'] ?? 0;
        $unitName = $_POST['unit_name'] ?? '';
        $newParentId = $_POST['parent_id'] ?? null;
        $newSortOrder = $_POST['sort_order'] ?? null;
        $code = $_POST['code'] ?? null;
        
        if(empty($unitName)) {
            echo json_encode(['status' => 'error', 'message' => '单位名称不能为空']);
            exit;
        }
        if(empty($unitId)) {
            echo json_encode(['status' => 'error', 'message' => '单位ID不能为空']);
            exit;
        }        
        // 获取当前单位信息
        $currentSql = "SELECT parent_id, sort_order, unit_name FROM unit WHERE id = ?";
        $currentStmt = $conn->prepare($currentSql);
        $currentStmt->bind_param('i', $unitId);
        $currentStmt->execute();
        $currentResult = $currentStmt->get_result();
        $currentRow = $currentResult->fetch_assoc();
        $currentParentId = $currentRow['parent_id'];
        $currentSortOrder = $currentRow['sort_order'];
        $currentUnitName = $currentRow['unit_name'];
        
        // 检查单位名称是否变化
        if ($unitName !== $currentUnitName) {
            // 检查新名称是否已存在
            $checkUniqueSql = "SELECT COUNT(*) as count FROM unit WHERE unit_name = ? AND id != ?";
            $checkUniqueStmt = $conn->prepare($checkUniqueSql);
            $checkUniqueStmt->bind_param('si', $unitName, $unitId);
            $checkUniqueStmt->execute();
            $uniqueResult = $checkUniqueStmt->get_result();
            $uniqueCount = $uniqueResult->fetch_assoc()['count'];
        
            if($uniqueCount > 0) {
                echo json_encode(['status' => 'error', 'message' => '该单位名已存在，请使用其他名称']);
                exit;
            }
        }
    
        // 获取上级单位名称用于日志
        $parentName = '';
        if ($newParentId) {
            $parentSql = "SELECT unit_name FROM unit WHERE id = ?";
            $parentStmt = $conn->prepare($parentSql);
            $parentStmt->bind_param('i', $newParentId);
            $parentStmt->execute();
            $parentResult = $parentStmt->get_result();
            $parentRow = $parentResult->fetch_assoc();
            $parentName = $parentRow['unit_name'] ?? '';
        }
        
        // 开始事务
        $conn->begin_transaction();
        
        try {
            // 更新当前单位
        
            // 如果上级单位发生变化
            if ($newParentId != $currentParentId) {
                // 更新原上级单位的子单位排序
                $updateOldSql = "UPDATE unit SET sort_order = sort_order - 1 WHERE parent_id = ? AND sort_order > ?";
                $updateOldStmt = $conn->prepare($updateOldSql);
                $updateOldStmt->bind_param('ii', $currentParentId, $currentSortOrder);
                $updateOldStmt->execute();
                
                // 更新新上级单位的子单位排序
                $updateNewSql = "UPDATE unit SET sort_order = sort_order + 1 WHERE parent_id = ? AND sort_order >= ?";
                $updateNewStmt = $conn->prepare($updateNewSql);
                $updateNewStmt->bind_param('ii', $newParentId, $newSortOrder);
                $updateNewStmt->execute();
            } 
            // 如果只是排序变化
            elseif ($newSortOrder != $currentSortOrder) {
                // 调整同级单位的排序
                if ($newSortOrder > $currentSortOrder) {
                    $updateSql = "UPDATE unit SET sort_order = sort_order - 1 WHERE parent_id = ? AND sort_order > ? AND sort_order <= ?";
                    $updateStmt = $conn->prepare($updateSql);
                    $updateStmt->bind_param('iii', $newParentId, $currentSortOrder, $newSortOrder);
                } else {
                    $updateSql = "UPDATE unit SET sort_order = sort_order + 1 WHERE parent_id = ? AND sort_order >= ? AND sort_order < ?";
                    $updateStmt = $conn->prepare($updateSql);
                    $updateStmt->bind_param('iii', $newParentId, $newSortOrder, $currentSortOrder);
                }
                $updateStmt->execute();
            }
            $sql = "UPDATE unit SET unit_name = ?, parent_id = ?, sort_order = ?, code = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('siiii', $unitName, $newParentId, $newSortOrder, $code, $unitId);
            $stmt->execute();
            
            // 提交事务
            $conn->commit();
            
            // 记录日志
            $operation = sprintf(
                "编辑单位: ID=%d, 新名称=%s, 上级单位=%s(ID=%d), 排序值=%d",
                $unitId,
                $unitName,
                $parentName,
                $newParentId,
                $newSortOrder
            );
            logOperation($conn, $_SESSION['user_id'], 2, $operation);
            
            echo json_encode(['status' => 'success', 'message' => '单位更新成功']);
        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            echo json_encode(['status' => 'error', 'message' => '单位更新失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'del':
        // 删除单位
        $unitId = $_POST['id'] ?? 0;
    
        // 检查是否有下级单位
        $checkChildSql = "SELECT COUNT(*) as count FROM unit WHERE parent_id = ?";
        $checkChildStmt = $conn->prepare($checkChildSql);
        $checkChildStmt->bind_param('i', $unitId);
        $checkChildStmt->execute();
        $childResult = $checkChildStmt->get_result();
        $childCount = $childResult->fetch_assoc()['count'];
    
        if($childCount > 0) {
            echo json_encode(['status' => 'error', 'message' => '该单位存在下级单位，不能删除']);
            exit;
        }
    
        // 先检查是否有用户关联该单位
        $checkSql = "SELECT COUNT(*) as count FROM user WHERE organization_unit = ? or work_unit = ? ";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param('ii', $unitId, $unitId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        $count = $result->fetch_assoc()['count'];
        
        if($count > 0) {
            echo json_encode(['status' => 'error', 'message' => '该单位下还有用户，不能删除']);
            exit;
        }
        
        // 获取单位名称用于日志记录
        $unitNameSql = "SELECT unit_name, parent_id, sort_order FROM unit WHERE id = ?";
        $unitNameStmt = $conn->prepare($unitNameSql);
        $unitNameStmt->bind_param('i', $unitId);
        $unitNameStmt->execute();
        $unitNameResult = $unitNameStmt->get_result();
        $unitNameRow = $unitNameResult->fetch_assoc();
        $unitName = $unitNameRow['unit_name'] ?? '';
        $parentId = $unitNameRow['parent_id'] ?? null;
        $sortOrder = $unitNameRow['sort_order'] ?? null;
    
        // 更新后续单位的排序值
        if ($parentId !== null && $sortOrder !== null) {
            $updateSql = "UPDATE unit SET sort_order = sort_order - 1 WHERE parent_id = ? AND sort_order > ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param('ii', $parentId, $sortOrder);
            $updateStmt->execute();
        }
    
        $sql = "DELETE FROM unit WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $unitId);
        if ($stmt->execute()) {
            // 记录删除日志
            $operation = "删除单位: " . $unitName . " (ID=" . $unitId . ")";
            logOperation($conn, $_SESSION['user_id'],2, $operation);
            
            echo json_encode(['status' => 'success', 'message' => '单位删除成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '单位删除失败']);
        }
        break;
        
    default:
        echo json_encode(['status' => 'error', 'message' => '无效操作']);
}
?>