<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位管理</title>
    <style>
        /* 全局样式 */
        * {
            margin: 5px;
            padding: 0px;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            color: #333;
        }

        /* 标题和添加按钮容器 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .title {
            font-size: 24px;
            font-weight: bold;
        }

        .add-button {
            padding: 8px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .add-icon {
            margin-right: 5px;
        }

        /* 单位树容器 */
        .unit-tree {
            margin: 15px;
        }

        /* 单位节点样式 */
        .unit {
            padding: 15px 20px; /* 增大内边距，原来为 10px 15px */
            border-radius: 3px;
            margin-bottom: 10px; /* 增大底部外边距，原来可能没有或者较小 */
            cursor: pointer;
        }

        .level-1 {
            background-color: #e3f2fd;
        }

        .level-2 {
            background-color: #f0f9ff;
            margin-left: 15px;
        }

        .level-3 {
            background-color: white;
            margin-left: 30px;
        }

        /* 单位名称和操作按钮容器 */
        .unit-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .unit-name {
            display: flex;
            align-items: center;
        }

        .unit-actions {
            display: flex;
            gap: 10px;
        }

        /* 操作按钮样式 */
        .action {
            padding: 3px 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-edit {
            background-color: #f0ad4e;
            color: white;
        }

        .action-delete {
            background-color: #d9534f;
            color: white;
        }

        .action-up {
            background-color: #5cb85c;
            color: white;
        }

        .action-down {
            background-color: #5bc0de;
            color: white;
        }

        .action-add {
            background-color: #5cb85c;
            color: white;
        }

        .caret {
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid #333;
            display: inline-block;
            margin-right: 5px;
            transition: transform 0.2s;
        }

        .caret-up {
            transform: rotate(-180deg);
        }
        
        /* 加载状态样式 */
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 50px;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .empty-text {
            font-size: 16px;
            color: #666;
        }

        /* 对话框样式 */
        dialog#add-unit-dialog {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 20px;
            width: 400px;
            max-width: 90%;
            margin: auto;
        }

        dialog#add-unit-dialog::backdrop {
            background-color: rgba(0, 0, 0, 0.3);
        }

        dialog#add-unit-dialog form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        dialog#add-unit-dialog h2 {
            margin-bottom: 10px;
            font-size: 20px;
            color: #333;
        }

        dialog#add-unit-dialog label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        dialog#add-unit-dialog input,
        dialog#add-unit-dialog select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        dialog#add-unit-dialog div {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }

        dialog#add-unit-dialog button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        dialog#add-unit-dialog button[type="submit"] {
            background-color: #007bff;
            color: white;
        }

        dialog#add-unit-dialog button[type="reset"] {
            background-color: #f0f0f0;
            color: #333;
        }

        dialog#add-unit-dialog button[type="submit"]:hover {
            background-color: #0056b3;
        }

        dialog#add-unit-dialog button[type="reset"]:hover {
            background-color: #e0e0e0;
        }

        /* 对话框样式 */
        dialog{
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 20px;
            width: 400px;
            max-width: 90%;
            margin: auto;
        }

        dialog::backdrop {
            background-color: rgba(0, 0, 0, 0.3);
        }

        dialog form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        dialog h2 {
            margin-bottom: 10px;
            font-size: 20px;
            color: #333;
        }

        dialog label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        dialog input,
        dialog select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        dialog div {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }

        dialog button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        dialog button[type="submit"] {
            background-color: #007bff;
            color: white;
        }

        dialog button[type="reset"] {
            background-color: #f0f0f0;
            color: #333;
        }

        dialog button[type="submit"]:hover {
            background-color: #0056b3;
        }

        dialog button[type="reset"]:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="title">单位管理</div>
        <button class="add-button" id="add-button">
            <span class="add-icon">+</span> 添加单位
        </button>
    </div>
    <div class="unit-tree" id="unit-tree">
        <div class="loading">
            <div>加载中...</div>
        </div>
    </div>

    <dialog id="add-unit-dialog">
        <form method="dialog">
            <h2>添加单位</h2>
            <label for="unit-name">单位名称：</label>
            <input type="text" id="unit-name" name="unit-name" required>
            <label for="unit-code">单位编码：</label>
            <input type="text" id="unit-code" name="unit-code">
            <label for="parent-unit">上级单位：</label>
            <select id="parent-unit" name="parent-unit">
                <!-- 这里的选项会在加载数据后动态填充 -->
            </select>
            <label for="unit-after">排序：</label>
            <select id="unit-after" name="unit-after">
                <!-- 这里的选项会在加载数据后动态填充 -->
            </select>
            <div>
                <button type="submit">确定</button>
                <button type="reset" value="cancel">取消</button>
            </div>
        </form>
    </dialog>
    <dialog id="edit-unit-dialog">
        <form method="dialog">
            <h2>编辑单位</h2>
            <label for="edit-unit-name">单位名称：</label>
            <input type="text" id="edit-unit-name" name="edit-unit-name" required>
            <label for="edit-unit-code">单位编码：</label>
            <input type="text" id="edit-unit-code" name="edit-unit-code">
            <label for="edit-parent-unit">上级单位：</label>
            <select id="edit-parent-unit" name="edit-parent-unit">
                <!-- 这里的选项会在加载数据后动态填充 -->
            </select>
            <div>
                <button type="submit">确定</button>
                <button type="reset" value="cancel">取消</button>
            </div>
        </form>
    </dialog>
    <script>
        // 树形结构
         let globalUnitData = null;
        // 扁平化结构
         let globalFlatUnitData = {};

         let currentEditUnitInfo = null; 


        // 获取对话框元素
        const addUnitDialog = document.getElementById('add-unit-dialog');
        const addButton = document.getElementById('add-button');

        // 点击添加单位按钮时显示对话框
        addButton.addEventListener('click', () => {
            addUnitDialog.showModal();
            populateParentUnitDropdown(globalUnitData);
            
            // 获取上级单位下拉框的当前值
            const parentUnitSelect = document.getElementById('parent-unit');
            if (parentUnitSelect.options.length > 0) {
                const selectedParentId = parentUnitSelect.value;
                populateUnitAfterDropdown(selectedParentId);
            }
        });

        // 添加单位：获取取消按钮
        const cancelButton = document.querySelector('dialog#add-unit-dialog button[type="reset"]');
        // 添加单位：为取消按钮添加点击事件监听器
        cancelButton.addEventListener('click', () => {
            addUnitDialog.close();
        });


        // 从API获取单位数据
        async function fetchUnitData() {
            try {
                const response = await fetch('../api/get_unit_info.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误，状态码: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.status === 'success' && result.data) {
                    // 清空加载状态
                    document.getElementById('unit-tree').innerHTML = '';
                    
                    // 渲染单位树 - 注意直接使用result.data，而不是result.data[0]
                    globalUnitData = result.data;

                                // 转换为扁平结构
                    function flattenTree(nodes) {
                        nodes.forEach(node => {
                            const { children, ...flatNode } = node;
                            globalFlatUnitData[node.id] = flatNode;
                            if (children) {
                                flattenTree(children);
                            }
                        });
                    }
                    flattenTree(Array.isArray(globalUnitData)? globalUnitData : [globalUnitData]);

                    console.log(globalFlatUnitData)
                    renderUnitTree(result.data);
                    
                    // 设置事件监听器
                    setupEventListeners();
                } else {
                    document.getElementById('unit-tree').innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">⚠️</div>
                            <p class="empty-text">未获取到单位数据</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('获取单位数据失败:', error);
                document.getElementById('unit-tree').innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">❌</div>
                        <p class="empty-text">加载单位数据失败: ${error.message}</p>
                    </div>
                `;
            }
        }

        // ... existing code ...

        // 递归渲染单位树
        function renderUnitTree(unit, parentElement = document.getElementById('unit-tree'), level = 1) {
            const unitElement = document.createElement('div');
            unitElement.className = `unit level-${level}`;
            unitElement.dataset.id = unit.id;
            unitElement.dataset.level = level;
            
            // 确定单位名称
            const unitName = unit.unit_name || '未命名单位';
            
            // 过滤掉 children 属性
            const { children, ...unitInfoWithoutChildren } = unit;
            const unitInfoJson = JSON.stringify(unitInfoWithoutChildren);
            
            // 构建单位内容
            let unitContent = `
                <div class="unit-content">
                    <div class="unit-name">
                        ${unit.children && unit.children.length > 0 ? '<span class="caret"></span>' : ''}
                        ${unitName}
                    </div>
                    <div class="unit-actions">
            `;
 

            // 添加子单位、编辑、删除、上移、下移
            unitContent += `
                <button class="action action-add" data-id="${unit.id}" data-level="${level}" data-unit-info="${encodeURIComponent(unitInfoJson)}">
                    <span>+</span>
                </button>
                <button class="action action-edit" data-id="${unit.id}" data-level="${level}" data-unit-info="${encodeURIComponent(unitInfoJson)}">
                    <span>✏️</span>
                </button>
                <button class="action action-delete" data-id="${unit.id}" data-level="${level}" data-unit-info="${encodeURIComponent(unitInfoJson)}">
                    <span>×</span>
                </button>
                <button class="action action-up" data-id="${unit.id}" data-level="${level}" data-unit-info="${encodeURIComponent(unitInfoJson)}">
                    <span>↑</span>
                </button>
                <button class="action action-down" data-id="${unit.id}" data-level="${level}" data-unit-info="${encodeURIComponent(unitInfoJson)}">
                    <span>↓</span>
                </button>
            `;
            
            unitContent += `
                    </div>
                </div>
            `;
            
            unitElement.innerHTML = unitContent;
            parentElement.appendChild(unitElement);
            
            // 如果有子单位，递归渲染
            if (unit.children && unit.children.length > 0) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = `level-${level + 1}-container`;
                unitElement.appendChild(childrenContainer);
                
                // 按sort_order排序子单位
                const sortedChildren = [...unit.children].sort((a, b) => a.sort_order - b.sort_order);
                
                sortedChildren.forEach(child => {
                    renderUnitTree(child, childrenContainer, level + 1);
                });
            }
        }



        // 设置事件监听器
        function setupEventListeners() {
            // 展开/折叠子单位
            document.querySelectorAll('.caret').forEach(caret => {
                caret.addEventListener('click', function() {
                    const unit = this.closest('.unit');
                    const childrenContainer = unit.querySelector(`.level-${parseInt(unit.dataset.level) + 1}-container`);
                    
                    if (childrenContainer) {
                        if (childrenContainer.style.display === 'none') {
                            childrenContainer.style.display = 'block';
                            this.classList.remove('caret-up');
                        } else {
                            childrenContainer.style.display = 'none';
                            this.classList.add('caret-up');
                        }
                    }
                });
            });

            // 删除按钮事件监听器
            document.querySelectorAll('.action-delete').forEach(deleteButton => {
                deleteButton.addEventListener('click', async () => {
                    const unitId = deleteButton.dataset.id;
                    const unitElement = deleteButton.closest('.unit');
                    const unitName = unitElement.querySelector('.unit-name').textContent.trim();

                    // 弹出确认对话框
                    const isConfirmed = confirm(`是否删除“${unitName}”？`);

                    if (isConfirmed) {
                        try {
                            // 发送删除请求
                            const response = await fetch('../api/unit_manage.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded'
                                },
                                body: `action=del&id=${unitId}`
                            });

                            if (!response.ok) {
                                throw new Error(`HTTP错误，状态码: ${response.status}`);
                            }

                            const result = await response.json();
                            if (result.status === 'success') {
                                // 重新加载单位数据
                                fetchUnitData();
                            } else {
                                console.error('删除单位失败:', result.message);
                                alert(`删除单位失败: ${result.message}`);
                            }
                        } catch (error) {
                            console.error('请求出错:', error);
                            alert(`请求出错: ${error.message}`);
                        }
                    }
                });
            });

        document.querySelectorAll('.action-add').forEach(addButton => {
            addButton.addEventListener('click', () => {
                const parentUnitId = addButton.dataset.id;
                addUnitDialog.showModal();
                populateParentUnitDropdown(globalUnitData);

                // 自动选中点击按钮对应的上级单位
                const parentUnitSelect = document.getElementById('parent-unit');
                Array.from(parentUnitSelect.options).forEach(option => {
                    if (option.value === parentUnitId) {
                        option.selected = true;
                    }
                });

                // 触发上级单位下拉框的 change 事件以填充“单位之后”下拉框
                parentUnitSelect.dispatchEvent(new Event('change'));
                
            });
        });
        document.querySelectorAll('.action-edit').forEach(editButton => {
            editButton.addEventListener('click', () => {
                const unitInfoJson = decodeURIComponent(editButton.dataset.unitInfo);
                const unitInfo = JSON.parse(unitInfoJson);
                currentEditUnitInfo = unitInfo; // 存储当前编辑的单位信息
                
                const editUnitDialog = document.getElementById('edit-unit-dialog');
                editUnitDialog.showModal();
                
                // 填充编辑对话框
                document.getElementById('edit-unit-name').value = unitInfo.unit_name;
                document.getElementById('edit-unit-code').value = unitInfo.code;
                
                // 填充编辑对话框的上级单位下拉框
                const editParentUnitSelect = document.getElementById('edit-parent-unit');
                populateParentUnitDropdown(globalUnitData, editParentUnitSelect);
            });
        });


        // 为上移按钮添加点击事件监听器
        document.addEventListener('click', function(event) { 
             if (event.target.closest('.action-up')) { 
                 const upButton = event.target.closest('.action-up'); 
                 const unitId = upButton.dataset.id; 
                 const unitInfo = globalFlatUnitData[unitId]; 
 
                 if (unitInfo) { 
                     const { unit_name, parent_id, sort_order } = unitInfo; 
                     const newSortOrder = sort_order - 1; 
 
                     const data = new URLSearchParams(); 
                     data.append('action', 'edit'); 
                     data.append('id', unitId); 
                     data.append('unit_name', unit_name); 
                     data.append('parent_id', parent_id); 
                     data.append('sort_order', newSortOrder); 
 
                     fetch('../api/unit_manage.php', { 
                         method: 'POST', 
                         headers: { 
                             'Content-Type': 'application/x-www-form-urlencoded' 
                         }, 
                         body: data 
                     }) 
                    .then(response => { 
                         if (!response.ok) { 
                             throw new Error('网络响应异常'); 
                         } 
                         return response.text(); 
                     }) 
                    .then(text => { 
                         console.log(text); 
                         // 上移成功，调用 fetchUnitData 刷新单位树 
                         fetchUnitData(); 
                     }) 
                    .catch(error => { 
                         console.error('请求出错:', error); 
                     }); 
                 } 
             } 
         }); 

         // 为下移按钮添加点击事件监听器
        document.addEventListener('click', function(event) { 
             if (event.target.closest('.action-down')) { 
                 const upButton = event.target.closest('.action-down'); 
                 const unitId = upButton.dataset.id; 
                 const unitInfo = globalFlatUnitData[unitId]; 
 
                 if (unitInfo) { 
                     const { unit_name, parent_id, sort_order } = unitInfo; 
                     const newSortOrder = sort_order + 1; 
 
                     const data = new URLSearchParams(); 
                     data.append('action', 'edit'); 
                     data.append('id', unitId); 
                     data.append('unit_name', unit_name); 
                     data.append('parent_id', parent_id); 
                     data.append('sort_order', newSortOrder); 
 
                     fetch('../api/unit_manage.php', { 
                         method: 'POST', 
                         headers: { 
                             'Content-Type': 'application/x-www-form-urlencoded' 
                         }, 
                         body: data 
                     }) 
                    .then(response => { 
                         if (!response.ok) { 
                             throw new Error('网络响应异常'); 
                         } 
                         return response.text(); 
                     }) 
                    .then(text => { 
                         console.log(text); 
                         // 上移成功，调用 fetchUnitData 刷新单位树 
                         fetchUnitData(); 
                     }) 
                    .catch(error => { 
                         console.error('请求出错:', error); 
                     }); 
                 } 
             } 
         }); 
  

            
            // 添加其他操作的事件监听器（编辑、删除、上移、下移等）
            // 这些功能需要根据实际需求实现对应的逻辑
        }

        // 填充上级单位下拉框
        // 填充上级单位下拉框
        function populateParentUnitDropdown(data, selectElement = document.getElementById('parent-unit')) {
            // 清空现有选项
            selectElement.innerHTML = '';
        
            // 递归函数，用于添加所有层级的单位
            function addOptions(nodes, level = 0) {
                nodes.forEach(node => {
                    const option = document.createElement('option');
                    option.value = node.id;
                    // 根据层级添加缩进
                    option.textContent = '  '.repeat(level) + node.unit_name;
                    selectElement.appendChild(option);
        
                    if (node.children && node.children.length > 0) {
                        addOptions(node.children, level + 1);
                    }
                });
            }
        
            if (Array.isArray(data)) {
                addOptions(data);
            } else if (data) {
                addOptions([data]);
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            fetchUnitData();
            // 为上级单位下拉列表添加事件监听器
            const parentUnitSelect = document.getElementById('parent-unit');
            parentUnitSelect.addEventListener('change', function() {
                populateUnitAfterDropdown(this.value);
            });
        });

        // 填充单位之后下拉框
        function populateUnitAfterDropdown(parentId) {
            const unitAfterSelect = document.getElementById('unit-after');
            // 清空现有选项
            unitAfterSelect.innerHTML = '';

            // 递归查找子单位
            function findChildren(data, id) {
                if (data.id === id) {
                    return data.children || [];
                }
                if (data.children) {
                    for (let child of data.children) {
                        const result = findChildren(child, id);
                        if (result) {
                            return result;
                        }
                    }
                }
                return [];
            }

            const children = findChildren(globalUnitData, parseInt(parentId));
            //添加一个sort_order为0的选项,名称为排在最前面的选项
            const optionFirst = document.createElement('option');
            optionFirst.value = 0;
            optionFirst.textContent = '排在最前面';
            unitAfterSelect.appendChild(optionFirst);
            children.forEach(child => {
                const option = document.createElement('option');
                // 此处下拉列表中填充的值是sort_order，而不是id
                option.value = child.sort_order;
                option.textContent = '在' + child.unit_name + '之后';
                unitAfterSelect.appendChild(option);
            });

            // 根据子单位数量决定是否禁用下拉框
            if (children.length === 0) {
                unitAfterSelect.disabled = true;
            } else {
                unitAfterSelect.disabled = false;
            }
        }

        // 获取编辑对话框和表单元素
        const editUnitDialog = document.getElementById('edit-unit-dialog');
        const editUnitForm = editUnitDialog.querySelector('form');

        // 为编辑对话框的取消按钮添加点击事件监听器
        editUnitForm.querySelector('button[type="reset"]').addEventListener('click', () => {
            editUnitDialog.close();
        });

        // 为编辑对话框的表单添加提交事件监听器
        editUnitForm.addEventListener('submit', async (event) => {
            event.preventDefault(); // 阻止表单默认提交行为

            // 获取表单数据
            const unitName = document.getElementById('edit-unit-name').value; 
            const unitCode = document.getElementById('edit-unit-code').value; 
            const parentUnitId = document.getElementById('edit-parent-unit').value; 
    
            const unitId = currentEditUnitInfo.id;
            const afterUnitId = globalFlatUnitData[unitId]?.afterUnitId || null; 

            try {
                // 发送编辑请求
                const response = await fetch('../api/unit_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `action=edit&id=${unitId}&unit_name=${encodeURIComponent(unitName)}&parent_id=${parentUnitId}&after_unit_id=${afterUnitId}&code=${encodeURIComponent(unitCode)}`
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误，状态码: ${response.status}`);
                }

                const result = await response.json();
                if (result.status === 'success') {
                    // 关闭对话框
                    editUnitDialog.close();
                    // 重新加载单位数据
                    fetchUnitData();
                } else {
                    console.error('编辑单位失败:', result.message);
                    alert(`编辑单位失败: ${result.message}`);
                }
            } catch (error) {
                console.error('请求出错:', error);
                alert(`请求出错: ${error.message}`);
            }
        });
    </script>
</body>

</html>

<script>
    // 获取表单元素
    const addUnitForm = document.querySelector('dialog#add-unit-dialog form');

    // 为表单添加提交事件监听器
    addUnitForm.addEventListener('submit', async (event) => {
        // 阻止表单默认提交行为
        event.preventDefault();

        // 获取表单输入值
        const unitName = document.getElementById('unit-name').value;
        const unitCode = document.getElementById('unit-code').value; // 获取单位编码
        const parentUnitSelect = document.getElementById('parent-unit');
        const parentId = parentUnitSelect.value;
        const unitAfterSelect = document.getElementById('unit-after');
        const afterUnitId = unitAfterSelect.disabled ? null : unitAfterSelect.value;

        // 动态构建请求的 body
        let requestBody = `action=add&unit_name=${encodeURIComponent(unitName)}&parent_id=${parentId}&code=${encodeURIComponent(unitCode)}`;

        if (afterUnitId !== null) {
            requestBody += `&after_unit_id=${afterUnitId}`;
        }

        try {
            // 发送请求到 api/unit_manage.php
            const response = await fetch('../api/unit_manage.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: requestBody
            });

            if (!response.ok) {
                throw new Error(`HTTP错误，状态码: ${response.status}`);
            }

            const result = await response.json();
            if (result.status === 'success') {
                // 重新加载单位数据
                fetchUnitData();
                addUnitDialog.close();
            } else {
                console.error('添加单位失败:', result.message);
                alert(`添加单位失败: ${result.message}`);
            }
        } catch (error) {
            console.error('请求出错:', error);
            alert(`请求出错: ${error.message}`);
        }
    });
</script>