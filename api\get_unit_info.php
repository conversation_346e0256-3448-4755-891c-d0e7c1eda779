<?php
require_once '../config.php';
//引入waf并检测输入参数是否有注入风险

$unit_id = $_POST['id'] ?? 0;

// 如果有ID参数，查询单个单位信息
if ($unit_id) {
    // 验证输入
    if (!is_numeric($unit_id) || $unit_id <= 0) {
        echo json_encode(['status' => 'error', 'message' => '无效的单位ID']);
        exit;
    }

    // 查询单位信息
    $sql = "SELECT id, unit_name, code, parent_id, sort_order FROM unit WHERE id = ?";
    $stmt = $conn->prepare($sql);

    // 检查prepare是否成功
    if (!$stmt) {
        error_log("SQL准备失败: " . $conn->error . " SQL: " . $sql);
        echo json_encode(['status' => 'error', 'message' => '数据库准备失败: ' . $conn->error]);
        exit;
    }

    $stmt->bind_param('i', $unit_id);
    if (!$stmt->execute()) {
        error_log("SQL执行失败: " . $stmt->error);
        echo json_encode(['status' => 'error', 'message' => '数据库查询失败']);
        exit;
    }

    $result = $stmt->get_result();
    $unit = $result->fetch_assoc();

    if (!$unit) {
        echo json_encode(['status' => 'error', 'message' => '单位不存在']);
        exit;
    }

    echo json_encode([
        'status' => 'success',
        'data' => $unit
    ]);
} else {
    // 查询所有单位信息
    $sql = "SELECT id, unit_name, code, parent_id, sort_order FROM unit ORDER BY sort_order ASC";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log("SQL准备失败: " . $conn->error . " SQL: " . $sql);
        echo json_encode(['status' => 'error', 'message' => '数据库准备失败: ' . $conn->error]);
        exit;
    }

    if (!$stmt->execute()) {
        error_log("SQL执行失败: " . $stmt->error);
        echo json_encode(['status' => 'error', 'message' => '数据库查询失败']);
        exit;
    }

    $result = $stmt->get_result();
    $units = [];
    while ($row = $result->fetch_assoc()) {
        $units[] = $row;
    }
// 查询所有单位信息
$sql = "SELECT id, unit_name, code, parent_id, sort_order FROM unit ORDER BY sort_order ASC";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    error_log("SQL准备失败: " . $conn->error . " SQL: " . $sql);
    echo json_encode(['status' => 'error', 'message' => '数据库准备失败: ' . $conn->error]);
    exit;
}

if (!$stmt->execute()) {
    error_log("SQL执行失败: " . $stmt->error);
    echo json_encode(['status' => 'error', 'message' => '数据库查询失败']);
    exit;
}

$result = $stmt->get_result();
$units = [];
while ($row = $result->fetch_assoc()) {
    $units[] = $row;
}

// 构建树状结构函数
    function buildTree(array $items, $parentId = null) {
        $branch = array();
        
        foreach ($items as $item) {
            if ($item['parent_id'] == $parentId) {
                $children = buildTree($items, $item['id']);
                if ($children) {
                    // 对子节点按sort_order排序
                    usort($children, function($a, $b) {
                        return $a['sort_order'] - $b['sort_order'];
                    });
                    $item['children'] = $children;
                }
                $branch[] = $item;
            }
        }
        
        // 对当前层级节点按sort_order排序
        usort($branch, function($a, $b) {
            return $a['sort_order'] - $b['sort_order'];
        });
        
        return $branch;
    }

    // 构建树状结构
    $tree = buildTree($units);

    echo json_encode([
        'status' => 'success',
        'data' => $tree[0]
    ]);

}
?>
