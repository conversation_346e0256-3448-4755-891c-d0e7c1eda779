import{_ as G,o as I,s as H,r as _,a as J,c as K,b as t,d as e,w as l,e as o,f as L,g as a,t as Q,h as W,i as X,E as Y}from"./index-C4srnUB3.js";const Z={class:"app-container"},$={class:"header"},ss={class:"header-left"},ts={class:"header-right"},es={class:"el-dropdown-link"},ls={class:"main-content"},os={class:"page-header"},as={class:"page-actions"},ns={class:"stat-cards"},ds={class:"charts"},rs={class:"chart-header"},is={class:"chart-header"},us={class:"card-header"},cs={__name:"App",setup(x){I(()=>{m()});const m=async()=>{try{const n=await H.get("/api/get_user_info.php");console.log("用户信息:",n.data)}catch(n){console.log("获取用户信息失败:",n)}},p=_(!1),T=_("dashboard"),w=_("month"),y=_("region"),O=J([{orderId:"ORD-20230501",customer:"张三",amount:"¥2,560.00",date:"2023-05-01",status:"success",statusText:"已完成"},{orderId:"ORD-20230502",customer:"李四",amount:"¥1,840.00",date:"2023-05-01",status:"pending",statusText:"处理中"},{orderId:"ORD-20230503",customer:"王五",amount:"¥3,620.00",date:"2023-04-30",status:"success",statusText:"已完成"},{orderId:"ORD-20230504",customer:"赵六",amount:"¥980.00",date:"2023-04-30",status:"canceled",statusText:"已取消"},{orderId:"ORD-20230505",customer:"孙七",amount:"¥1,250.00",date:"2023-04-29",status:"success",statusText:"已完成"}]),D=()=>{p.value=!p.value},k=n=>{console.log("dropdown command:",n)},z=(n,s)=>{console.log(n,s)},R=(n,s)=>{console.log(n,s)};return I(()=>{}),(n,s)=>{const i=o("el-button"),A=o("el-badge"),B=o("el-avatar"),v=o("el-dropdown-item"),E=o("el-dropdown-menu"),N=o("el-dropdown"),d=o("el-menu-item"),V=o("el-sub-menu"),U=o("el-menu"),M=o("el-aside"),C=o("el-breadcrumb-item"),P=o("el-breadcrumb"),u=o("el-card"),f=o("el-radio-button"),S=o("el-radio-group"),b=o("el-option"),h=o("el-select"),c=o("el-table-column"),j=o("el-tag"),q=o("el-table"),F=o("el-main");return L(),K("div",Z,[t("div",$,[t("div",ss,[e(i,{onClick:D,type:"text"},{default:l(()=>s[2]||(s[2]=[t("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[2]}),s[3]||(s[3]=t("span",{class:"logo"},"CloudPivot",-1))]),t("div",ts,[e(A,{value:5,class:"item"},{default:l(()=>[e(i,{icon:"el-icon-bell",type:"text"})]),_:1}),e(N,{onCommand:k},{dropdown:l(()=>[e(E,null,{default:l(()=>[e(v,{command:"profile"},{default:l(()=>s[6]||(s[6]=[a("个人信息")])),_:1,__:[6]}),e(v,{command:"settings"},{default:l(()=>s[7]||(s[7]=[a("设置")])),_:1,__:[7]}),e(v,{command:"logout"},{default:l(()=>s[8]||(s[8]=[a("退出登录")])),_:1,__:[8]})]),_:1})]),default:l(()=>[t("span",es,[e(B,{size:"small",src:"https://picsum.photos/200/200?random=1"}),s[4]||(s[4]=t("span",{class:"user-name"},"管理员",-1)),s[5]||(s[5]=t("i",{class:"el-icon-caret-bottom el-icon--right"},null,-1))])]),_:1})])]),t("div",ls,[e(M,{width:p.value?"64px":"200px",class:"sidebar"},{default:l(()=>[e(U,{"default-active":T.value,class:"el-menu-vertical",mode:"vertical",collapse:p.value,onOpen:z,onClose:R},{default:l(()=>[e(d,{index:"dashboard"},{title:l(()=>s[9]||(s[9]=[t("i",{class:"el-icon-s-home"},null,-1),t("span",null,"仪表盘",-1)])),_:1}),e(V,{index:"system"},{title:l(()=>s[10]||(s[10]=[t("i",{class:"el-icon-s-tools"},null,-1),t("span",null,"系统管理",-1)])),default:l(()=>[e(d,{index:"user"},{title:l(()=>s[11]||(s[11]=[a("用户管理")])),_:1}),e(d,{index:"role"},{title:l(()=>s[12]||(s[12]=[a("角色管理")])),_:1}),e(d,{index:"permission"},{title:l(()=>s[13]||(s[13]=[a("权限管理")])),_:1})]),_:1}),e(V,{index:"business"},{title:l(()=>s[14]||(s[14]=[t("i",{class:"el-icon-s-order"},null,-1),t("span",null,"业务管理",-1)])),default:l(()=>[e(d,{index:"order"},{title:l(()=>s[15]||(s[15]=[a("订单管理")])),_:1}),e(d,{index:"product"},{title:l(()=>s[16]||(s[16]=[a("产品管理")])),_:1}),e(d,{index:"customer"},{title:l(()=>s[17]||(s[17]=[a("客户管理")])),_:1})]),_:1}),e(d,{index:"charts"},{title:l(()=>s[18]||(s[18]=[t("i",{class:"el-icon-pie-chart"},null,-1),t("span",null,"数据报表",-1)])),_:1}),e(d,{index:"settings"},{title:l(()=>s[19]||(s[19]=[t("i",{class:"el-icon-setting"},null,-1),t("span",null,"系统设置",-1)])),_:1})]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),e(F,{class:"content"},{default:l(()=>[e(P,{"separator-class":"el-icon-arrow-right"},{default:l(()=>[e(C,{to:{path:"/"}},{default:l(()=>s[20]||(s[20]=[a("首页")])),_:1,__:[20]}),e(C,null,{default:l(()=>s[21]||(s[21]=[a("仪表盘")])),_:1,__:[21]})]),_:1}),t("div",os,[s[24]||(s[24]=t("h1",{class:"page-title"},"系统概览",-1)),t("div",as,[e(i,{type:"primary",icon:"el-icon-refresh"},{default:l(()=>s[22]||(s[22]=[a("刷新")])),_:1,__:[22]}),e(i,{type:"success",icon:"el-icon-download"},{default:l(()=>s[23]||(s[23]=[a("导出")])),_:1,__:[23]})])]),t("div",ns,[e(u,{class:"stat-card",shadow:"hover"},{default:l(()=>s[25]||(s[25]=[t("div",{class:"card-icon bg-primary"},[t("i",{class:"el-icon-user"})],-1),t("div",{class:"card-content"},[t("div",{class:"card-title"},"总用户数"),t("div",{class:"card-value"},"12,845"),t("div",{class:"card-trend"},[t("span",{class:"up"},"+12.5%"),t("span",{class:"text"},"较上月")])],-1)])),_:1,__:[25]}),e(u,{class:"stat-card",shadow:"hover"},{default:l(()=>s[26]||(s[26]=[t("div",{class:"card-icon bg-success"},[t("i",{class:"el-icon-s-order"})],-1),t("div",{class:"card-content"},[t("div",{class:"card-title"},"今日订单"),t("div",{class:"card-value"},"386"),t("div",{class:"card-trend"},[t("span",{class:"up"},"+8.2%"),t("span",{class:"text"},"较昨日")])],-1)])),_:1,__:[26]}),e(u,{class:"stat-card",shadow:"hover"},{default:l(()=>s[27]||(s[27]=[t("div",{class:"card-icon bg-warning"},[t("i",{class:"el-icon-money"})],-1),t("div",{class:"card-content"},[t("div",{class:"card-title"},"今日销售额"),t("div",{class:"card-value"},"¥128,640"),t("div",{class:"card-trend"},[t("span",{class:"down"},"-2.1%"),t("span",{class:"text"},"较昨日")])],-1)])),_:1,__:[27]}),e(u,{class:"stat-card",shadow:"hover"},{default:l(()=>s[28]||(s[28]=[t("div",{class:"card-icon bg-danger"},[t("i",{class:"el-icon-message"})],-1),t("div",{class:"card-content"},[t("div",{class:"card-title"},"待处理消息"),t("div",{class:"card-value"},"16"),t("div",{class:"card-trend"},[t("span",{class:"up"},"+5.3%"),t("span",{class:"text"},"较昨日")])],-1)])),_:1,__:[28]})]),t("div",ds,[e(u,{class:"chart-card",shadow:"hover"},{default:l(()=>[t("div",rs,[s[32]||(s[32]=t("h3",null,"销售趋势",-1)),e(S,{modelValue:w.value,"onUpdate:modelValue":s[0]||(s[0]=r=>w.value=r),size:"small"},{default:l(()=>[e(f,{label:"week"},{default:l(()=>s[29]||(s[29]=[a("本周")])),_:1,__:[29]}),e(f,{label:"month"},{default:l(()=>s[30]||(s[30]=[a("本月")])),_:1,__:[30]}),e(f,{label:"year"},{default:l(()=>s[31]||(s[31]=[a("全年")])),_:1,__:[31]})]),_:1},8,["modelValue"])]),s[33]||(s[33]=t("div",{class:"chart-container"},[t("div",{class:"placeholder-chart",style:{height:"300px"}})],-1))]),_:1,__:[33]}),e(u,{class:"chart-card",shadow:"hover"},{default:l(()=>[t("div",is,[s[34]||(s[34]=t("h3",null,"用户分布",-1)),e(h,{modelValue:y.value,"onUpdate:modelValue":s[1]||(s[1]=r=>y.value=r),size:"small",placeholder:"选择类型"},{default:l(()=>[e(b,{label:"地区分布",value:"region"}),e(b,{label:"年龄分布",value:"age"}),e(b,{label:"性别分布",value:"gender"})]),_:1},8,["modelValue"])]),s[35]||(s[35]=t("div",{class:"chart-container"},[t("div",{class:"placeholder-chart",style:{height:"300px"}})],-1))]),_:1,__:[35]})]),e(u,{class:"table-card",shadow:"hover"},{default:l(()=>[t("div",us,[s[37]||(s[37]=t("h3",null,"最近订单",-1)),e(i,{type:"text",size:"small"},{default:l(()=>s[36]||(s[36]=[a("查看全部")])),_:1,__:[36]})]),e(q,{data:O,stripe:"",style:{width:"100%"}},{default:l(()=>[e(c,{prop:"orderId",label:"订单编号"}),e(c,{prop:"customer",label:"客户名称"}),e(c,{prop:"amount",label:"金额"}),e(c,{prop:"date",label:"日期"}),e(c,{prop:"status",label:"状态"},{default:l(r=>[e(j,{type:r.row.status==="pending"?"warning":r.row.status==="success"?"success":"info"},{default:l(()=>[a(Q(r.row.statusText),1)]),_:2},1032,["type"])]),_:1}),e(c,{label:"操作"},{default:l(r=>[e(i,{type:"text",size:"small"},{default:l(()=>s[38]||(s[38]=[a("查看")])),_:1,__:[38]}),e(i,{type:"text",size:"small"},{default:l(()=>s[39]||(s[39]=[a("编辑")])),_:1,__:[39]})]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})])])}}},ps=G(cs,[["__scopeId","data-v-7cf5752f"]]),g=W(ps);g.use(X);for(const[x,m]of Object.entries(Y))g.component(x,m);g.mount("#app");
