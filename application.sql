-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- 主机： 80.167.1.4
-- 生成日期： 2025-05-30 08:00:28
-- 服务器版本： 8.4.5
-- PHP 版本： 8.3.6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `application`
--

-- --------------------------------------------------------

--
-- 表的结构 `2_unit`
--

CREATE TABLE `2_unit` (
  `id` int NOT NULL COMMENT '自增ID，主键',
  `unit_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单位名称，唯一且非空',
  `parent_id` int DEFAULT NULL COMMENT '上级单位ID，可以为空',
  `sort_order` int NOT NULL COMMENT '排序字段，用于控制显示顺序，非空',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织机构代码'
) ;

--
-- 转存表中的数据 `2_unit`
--

INSERT INTO `2_unit` (`id`, `unit_name`, `parent_id`, `sort_order`, `code`) VALUES
(1, '岳池县', NULL, 1, '511621');

-- --------------------------------------------------------

--
-- 表的结构 `3_user`
--

CREATE TABLE `3_user` (
  `id` int NOT NULL COMMENT '自增ID，主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `id_number` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '身份证号',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `archive_birthdate` date NOT NULL COMMENT '档案出生时间',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别 (0:未知 1:男 2:女)',
  `short_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短号',
  `alt_phone_1` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码2',
  `alt_phone_2` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码3',
  `landline` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '座机',
  `organization_unit` int DEFAULT NULL COMMENT '编制单位',
  `work_unit` int DEFAULT NULL COMMENT '工作单位',
  `employment_date` date DEFAULT NULL COMMENT '参工时间',
  `political_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '政治面貌',
  `party_join_date` date DEFAULT NULL COMMENT '加入组织时间',
  `personnel_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '人员身份',
  `police_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '警号/辅警号',
  `is_assisting_officer` tinyint(1) DEFAULT NULL COMMENT '带辅民警 (0:否 1:是)',
  `employment_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '人员状态',
  `job_rank` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职级',
  `current_rank_date` date DEFAULT NULL COMMENT '任现职级时间',
  `position` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职务',
  `current_position_date` date DEFAULT NULL COMMENT '任现职务时间',
  `desc` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `3_user`
--

INSERT INTO `3_user` (`id`, `name`, `id_number`, `phone`, `password`, `archive_birthdate`, `gender`, `short_code`, `alt_phone_1`, `alt_phone_2`, `landline`, `organization_unit`, `work_unit`, `employment_date`, `political_status`, `party_join_date`, `personnel_type`, `police_number`, `is_assisting_officer`, `employment_status`, `job_rank`, `current_rank_date`, `position`, `current_position_date`, `desc`) VALUES
(1, '黄透平', '513621198303180058', '13551623030', '$2y$10$KOxDLFcbagE5/188xa0o3OSLyOaGyTpkTd1Km3pvHBmM2y0dZHmHK', '1970-01-01', 1, '623030', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

-- --------------------------------------------------------

--
-- 表的结构 `3_user_Role`
--

CREATE TABLE `3_user_Role` (
  `id` int NOT NULL COMMENT '自增id唯一',
  `userId` int NOT NULL COMMENT '用户id',
  `roleId` int NOT NULL COMMENT '用户角色ID',
  `unitId` int NOT NULL COMMENT '用户单位ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `4_Role_List`
--

CREATE TABLE `4_Role_List` (
  `id` int NOT NULL COMMENT '自增ID',
  `roleName` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `roleDesc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `4_Role_List`
--

INSERT INTO `4_Role_List` (`id`, `roleName`, `roleDesc`) VALUES
(1, '局领导', ''),
(2, '指挥长', ''),
(3, '值班长', ''),
(4, '值班员', ''),
(5, '技术员', ''),
(6, '内部安全员', '');

-- --------------------------------------------------------

--
-- 表的结构 `5_application`
--

CREATE TABLE `5_application` (
  `id` int NOT NULL COMMENT '自增ID，主键',
  `application_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称，唯一且非空',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用首页的URL',
  `public` tinyint(1) DEFAULT NULL COMMENT '应用是否公用，是为1，否为0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `5_application`
--

INSERT INTO `5_application` (`id`, `application_name`, `url`, `public`) VALUES
(1, 'system', NULL, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `6_duty_sched`
--

CREATE TABLE `6_duty_sched` (
  `id` int NOT NULL COMMENT '自增id唯一',
  `sched_date` date NOT NULL COMMENT '值班时间',
  `a_dir` int NOT NULL COMMENT 'A岗局领导用户ID',
  `b_dir` int NOT NULL COMMENT 'B岗局领导用户ID',
  `a_com` int NOT NULL COMMENT 'A岗指挥长用户ID',
  `b_com` int NOT NULL COMMENT 'B岗指挥长用户ID',
  `sub` int NOT NULL COMMENT '值班长用户ID',
  `op` json NOT NULL COMMENT '值班员用户ID，（存储多个用户id）',
  `tech` int NOT NULL COMMENT '技术支持人员用户ID',
  `sec` json NOT NULL COMMENT '安全员用户ID（存储多个用户id）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `log`
--

CREATE TABLE `log` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `application_id` int NOT NULL COMMENT '应用ID',
  `operation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作内容',
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作者的IP地址'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `log`
--

INSERT INTO `log` (`id`, `user_id`, `application_id`, `operation`, `timestamp`, `ip`) VALUES
(2, 1, 1, '登录成功', '2025-05-27 11:09:57', '*************'),
(4, 1, 1, '用户退出登录', '2025-05-27 11:13:22', '*************'),
(5, 1, 1, '登录成功', '2025-05-27 11:13:35', '*************'),
(6, 1, 1, '用户退出登录', '2025-05-27 15:36:01', '*************'),
(7, 1, 1, '登录成功', '2025-05-27 15:45:08', '*************'),
(8, 1, 1, '登录成功', '2025-05-27 15:45:30', '*************'),
(9, 1, 1, '登录成功', '2025-05-27 15:49:57', '*************'),
(10, 1, 1, '登录成功', '2025-05-27 15:50:21', '*************'),
(11, 1, 1, '用户退出登录', '2025-05-27 15:50:52', '*************'),
(12, 1, 1, '登录成功', '2025-05-27 15:51:04', '*************'),
(13, 1, 1, '用户退出登录', '2025-05-27 15:51:14', '*************'),
(14, 1, 1, '登录成功', '2025-05-27 15:52:10', '*************'),
(15, 1, 1, '用户退出登录', '2025-05-27 15:53:11', '*************'),
(16, 1, 1, '登录成功', '2025-05-27 15:53:33', '*************'),
(17, 1, 1, '用户退出登录', '2025-05-27 15:53:35', '*************'),
(18, 1, 1, '登录成功', '2025-05-27 15:54:31', '*************'),
(19, 1, 1, '用户退出登录', '2025-05-27 15:54:35', '*************'),
(20, 1, 1, '登录成功', '2025-05-27 15:54:41', '*************'),
(21, 1, 1, '用户退出登录', '2025-05-27 15:54:44', '*************'),
(22, 1, 1, '登录失败: 身份证号不存在', '2025-05-27 16:06:43', '*************6'),
(23, 1, 1, '登录失败: 身份证号不存在', '2025-05-27 16:06:46', '*************6'),
(24, 1, 1, '登录成功', '2025-05-27 16:11:23', '*************6'),
(25, 1, 1, '登录成功', '2025-05-28 09:15:03', '*************');

-- --------------------------------------------------------

--
-- 表的结构 `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int NOT NULL COMMENT '主键，自增ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `attempts` int DEFAULT '0' COMMENT '当前尝试登录次数',
  `last_attempt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上一次尝试登录时间',
  `locked_until` timestamp NULL DEFAULT NULL COMMENT '解锁时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `2_unit`
--
ALTER TABLE `2_unit`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unit_name` (`unit_name`);

--
-- 表的索引 `3_user`
--
ALTER TABLE `3_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id_number` (`id_number`);

--
-- 表的索引 `3_user_Role`
--
ALTER TABLE `3_user_Role`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `4_Role_List`
--
ALTER TABLE `4_Role_List`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `5_application`
--
ALTER TABLE `5_application`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `6_duty_sched`
--
ALTER TABLE `6_duty_sched`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `log`
--
ALTER TABLE `log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `application_id` (`application_id`);

--
-- 表的索引 `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `2_unit`
--
ALTER TABLE `2_unit`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键';

--
-- 使用表AUTO_INCREMENT `3_user`
--
ALTER TABLE `3_user`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键', AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `3_user_Role`
--
ALTER TABLE `3_user_Role`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id唯一';

--
-- 使用表AUTO_INCREMENT `4_Role_List`
--
ALTER TABLE `4_Role_List`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID', AUTO_INCREMENT=14;

--
-- 使用表AUTO_INCREMENT `5_application`
--
ALTER TABLE `5_application`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键', AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `6_duty_sched`
--
ALTER TABLE `6_duty_sched`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id唯一';

--
-- 使用表AUTO_INCREMENT `log`
--
ALTER TABLE `log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- 使用表AUTO_INCREMENT `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '主键，自增ID';

--
-- 限制导出的表
--

--
-- 限制表 `log`
--
ALTER TABLE `log`
  ADD CONSTRAINT `log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `3_user` (`id`),
  ADD CONSTRAINT `log_ibfk_2` FOREIGN KEY (`application_id`) REFERENCES `5_application` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
