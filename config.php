﻿<?php
require_once __DIR__ .'\conn_waf.php';

// 在第 79 行添加 session 检查
$testMessage = isset($_SESSION['test']) ? $_SESSION['test'] : '';
if (!isset($_SESSION['user_id'])||(isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800))) {
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => '未登录或登录信息已过期']);
    exit;
}

$_SESSION['last_activity'] = time(); // 更新最后活动时间

?>