// js/axios.js
// 引入全局 axios（UMD 格式已挂载到 window.axios）
const axios = window.axios;

// 创建 axios 实例
const service = axios.create({
  baseURL: 'http://127.0.0.1/',
  timeout: 5000
});

// 配置响应拦截器（全局生效）
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.status === 'error') {
      // 先弹出提示框显示错误信息
      alert(res.message || '未知错误');
      
      // 再进行进一步检查
      if (res.message ===  '未登录或登录信息已过期') {
        window.location.href = '../login.html'; // 统一跳转逻辑
        return Promise.reject(new Error('未登录，重定向到登录页'));
      }
      
      return Promise.reject(new Error(res.message));
    }
    return res;
  },
  error => {
    console.error('响应错误:', error);
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '../login.html';
    }
    alert(error.message || '网络错误');
    return Promise.reject(error);
  }
);

// 导出实例
window.service = service;  