<?php
require '..\conn_waf.php';

// 启动session
$_SESSION['test'] = 'ok';
ini_set('display_errors',1);
error_reporting(E_ALL);
// 添加POST参数获取
$id_number = $_POST['id_number'] ?? '';
$password = $_POST['password'] ?? '';



// 修改验证条件（原验证代码保持不变）
if (empty($id_number) || empty($password)) {
    echo json_encode(['status' => 'error', 'message' => '身份证号和密码不能为空']);
    exit;
}

// 查询用户


$sql = "SELECT id, name, work_unit ,organization_unit, password, id_number,personnel_type FROM user WHERE id_number = ?";

$stmt = $conn->prepare($sql);


// 添加prepare错误检查
if (!$stmt) {
    error_log("预处理失败: " . $conn->error);
    echo json_encode(['status' => 'error', 'message' => '系统错误']);
    exit;
}

$stmt->bind_param('i', $id_number);  // 第24行
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();


// 修改后
if (!$user) {
    error_log("登录失败 - 身份证号不存在: " . $id_number);
    logOperation($conn, 1, 1, "登录失败: 身份证号不存在");
    echo json_encode(['status' => 'error', 'message' => '身份证号不存在']);
    exit;
}

// 验证密码
// 在文件开头添加
$ip = $_SERVER['REMOTE_ADDR'];

// 检查IP是否被锁定
$lock_check = $conn->prepare("SELECT locked_until FROM login_attempts WHERE ip_address = ? AND locked_until > NOW()");
$lock_check->bind_param('s', $ip);
$lock_check->execute();
$lock_result = $lock_check->get_result();

if ($lock_result->num_rows > 0) {
    echo json_encode(['status' => 'error', 'message' => '您的IP已被锁定，请5分钟后再试']);
    exit;
}

// 在密码验证失败后添加
if (!password_verify($password, $user['password'])) {
    // 更新失败尝试次数
    $stmt = $conn->prepare("INSERT INTO login_attempts (ip_address, attempts) VALUES (?, 1) ON DUPLICATE KEY UPDATE attempts = attempts + 1, last_attempt = NOW()");
    $stmt->bind_param('s', $ip);
    $stmt->execute();
    
    // 检查是否达到5次失败
    $check = $conn->prepare("SELECT attempts FROM login_attempts WHERE ip_address = ?");
    $check->bind_param('s', $ip);
    $check->execute();
    $result = $check->get_result();
    $data = $result->fetch_assoc();
    
    if ($data['attempts'] >= 5) {
        // 锁定IP 5分钟
        $lock = $conn->prepare("UPDATE login_attempts SET locked_until = DATE_ADD(NOW(), INTERVAL 5 MINUTE) WHERE ip_address = ?");
        $lock->bind_param('s', $ip);
        $lock->execute();
    }
    
    echo json_encode(['status' => 'error', 'message' => '密码错误']);
    exit;
}

// 登录成功时清除失败记录
$clear = $conn->prepare("DELETE FROM login_attempts WHERE ip_address = ?");
$clear->bind_param('s', $ip);
$clear->execute();

// 设置Session
$_SESSION['user_id'] = $user['id'];
$_SESSION['username'] = $user['name'];
$_SESSION['unit_id'][0] = $user['organization_unit'];
if($user['work_unit'])
{
   $_SESSION['unit_id'][1] = $user['work_unit']; 
}

$_SESSION['personnel_type'] = $user['personnel_type'];//人员身份
$_SESSION['last_activity'] = time();

// 查询用户权限
$permissionSql = "SELECT application_id, permission_type FROM permission WHERE user_id = ?";
$permissionStmt = $conn->prepare($permissionSql);
$permissionStmt->bind_param('i', $user['id']);
$permissionStmt->execute();
$permissionResult = $permissionStmt->get_result();
 false;
// 将权限存入session字典

$_SESSION['permissions'] = array();

while ($row = $permissionResult->fetch_assoc()) {
    // 为当前行创建一个子数组
    $permission_array = array(
        "application_id" => $row['application_id'],
        "permission_type" => $row['permission_type']
    );
//如果app_id为1且权限为1的设置为系统管理员
    if($row['application_id']==1)
    {
       $_SESSION['is_admin'] = true;
    }
    // 将子数组存储到 $_SESSION['permissions'] 中

    $_SESSION['permissions'][]=$permission_array;
}


   
logOperation($conn, $user['id'], 1, "登录成功");
echo json_encode([
    'status' => 'success',
    'user' => [
        'id' => $_SESSION['user_id'],
        'name' => $_SESSION['username'],
        'unit_id' => $_SESSION['unit_id'],
        'is_admin' => $_SESSION['is_admin']
    ]
]);


?>
